<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0,minimal-ui:ios" name="viewport">
    <title>旭爱 我的毕业季flag</title>
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/style.min.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/animate.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/<EMAIL>">
    <link rel="stylesheet" href="css/index.css?v={php} echo mt_rand(1000,999999);{/php}">
    <script defer src="css/polyfill.js?v={php} echo mt_rand(1000,999999);{/php}"></script>
    <style>
        [v-cloak] {
            display: none;
        }
    </style>
</head>

<body>
    <div id="app" class="warp" v-cloak>
        <div class="musicbtn" :class="{ on:on }"  @click="bgClick"></div>
        <!-- 首页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===1">
            <img src="img/logo.png" class="logo animate__animated animate__fadeInLeft">
            <div class="rotation animate__animated animate__fadeIn">
                <van-swipe ref="swipe" class="my-swipe" :autoplay="2000" :show-indicators="false" indicator-color="white" vertical>
                    <van-swipe-item v-for="(item,index) in handleRotation">
                        <p>{{item[0]}}<span>{{item[1]}}</span>{{item[2]}}</p>
                    </van-swipe-item>
                </van-swipe>
            </div>
            <img class="title animate__animated animate__zoomIn" src="img/title.png">
            <div class="start pulsate-bck" @click="start"></div>
            <div class="button_container">
                <img src="img/button2.png" class="button animate__animated animate__fadeInLeft" @click="page=2">
                <img src="img/button3.png" class="button animate__animated animate__fadeInRight" @click="page=3">
            </div>
        </div>
        <!-- 活动规则页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===2">
            <img src="img/logo.png" class="logo animate__animated animate__fadeInLeft">
            <img src="img/title.png" class="title2 animate__animated animate__zoomIn">
            <div class="area">
                <img src="img/button2.png" class="stit">
                <div class="rule" v-html="startData.rule"></div>
                <img src="img/button4.png" class="back animate__animated animate__fadeInUp" @click="page=1">
            </div>
        </div>
        <!-- 我的奖品页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===3">
            <img src="img/logo.png" class="logo animate__animated animate__fadeInLeft">
            <div class="area area2">
                <img src="img/button3_1.png" class="stit">
                <div class="prize">
                    <div class="info">
                        <img src="img/jptit1.png" class="jptit mt5">
                        <p class="p2 p3 mt5" v-if="startData.prize">
                            {{startData.prizeTime}}：{{startData.ad2.replaceAll("\n", "")}}</p>
                        <div class="p2" v-if="startData.prize">{{startData.prize}}</div>
                        <p class="p2 p1 mt5" v-else>暂未中奖</p>
                    </div>
                    <div class="info">
                        <img src="img/jptit2.png" class="jptit">
                        <p class="p2 mt5">{{startData.userInfo.name}}&nbsp;&nbsp;{{startData.userInfo.phone}}</p>
                        <p class="p2">{{startData.userInfo.area.split(',').join('')}}</p>
                        <p class="p2">{{startData.userInfo.address}}</p>
                    </div>
                    <img src="img/edit.png" class="edit" @click="edit">
                </div>
                <img src="img/button4_1.png" class="back animate__animated animate__fadeInUp" @click="page=1">
            </div>
        </div>
        <!-- 登记信息页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===4">
            <img src="img/logo.png" class="logo animate__animated animate__fadeInLeft">
            <div class="area area2">
                <form class="form">
                    <img src="img/button9.png" class="stit">
                    <div class="form-item">
                        <label>姓　　名:</label>
                        <input type="text" v-model="form.name">
                    </div>
                    <div class="form-item">
                        <label>联系方式:</label>
                        <input type="number" v-model="form.phone">
                    </div>
                    <div class="form-item fs">
                        <label>邮寄地址:</label>
                        <div class="right" @click="focus">
                            <input type="text" placeholder="选择省" v-model="form.area.split(',')[0]" readonly>
                            <input type="text" placeholder="选择市" v-model="form.area.split(',')[1]" readonly>
                            <input type="text" placeholder="选择区" v-model="form.area.split(',')[2]" readonly>
                        </div>
                    </div>
                    <div class="form-item">
                        <label>街道地址:</label>
                        <input type="text" v-model="form.address" @keyup.enter="submit">
                    </div>
                    <div class="form-footer">
                        <p class="fz1">◎</p>
                        <p>免责声明:<br>本活动专题收集的所有信息仅供发放活动奖品使用，在未经得本人同意情况下绝对不会将您的任何资料以任何方式泄露给第三方。由于您自身原因如共享登录账号等导致的个人信息披露，活动方概不负责。
                        </p>
                    </div>
                    <img src="img/button6.png" class="back animate__animated animate__fadeInUp" @click="submit">
                </form>
                <van-popup v-model:show="popupShow" round position="bottom">
                    <van-picker show-toolbar title="请选择地区" :columns="options" default-index="11"
                        v-model="selectedValues" @cancel="popupShow = false" @confirm="onConfirm"></van-picker>
                </van-popup>
            </div>
        </div>
        <!-- 选择页 -->
        <div class="page fc bj2" :class="{blur:show||showpop}" v-if="page===5">
            <img src="img/logo.png" class="logo animate__animated animate__fadeInLeft">
            <img src="img/title.png" class="title3 animate__animated animate__zoomIn">
            <p class="tip">*该图由AI技术生成，仅为示意图，仅供游戏概念使用</p>
            <div class="arrow_right pulsate-bck2" @click="page=6"></div>
            <div class="button_container">
                <img src="img/button7.png" class="button animate__animated animate__fadeInLeft" @click="next(0)">
                <img src="img/button8.png" class="button animate__animated animate__fadeInRight" @click="showpop=1">
            </div>
        </div>
        <!-- 选择页 -->
        <div class="page fc bj3" :class="{blur:show||showpop}" v-if="page===6">
            <img src="img/logo.png" class="logo animate__animated animate__fadeInLeft">
            <img src="img/title.png" class="title3 animate__animated animate__zoomIn">
            <p class="tip">*该图由AI技术生成，仅为示意图，仅供游戏概念使用</p>
            <div class="arrow pulsate-bck2" @click="page=5"></div>
            <div class="button_container">
                <img src="img/button7.png" class="button animate__animated animate__fadeInLeft" @click="next(1)">
                <img src="img/button8.png" class="button animate__animated animate__fadeInRight" @click="showpop=2">
            </div>
        </div>
        <!-- 游戏页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===7">
            <div class="game_area">
                <div class="data">
                    <div class="time">
                        <span>倒计时</span>
                        <span>{{gameState.timer}}s</span>
                    </div>
                    <div class="score">{{gameState.score}}</div>
                </div>
                <canvas ref="gameCanvas" width="645" height="1015"></canvas>
            </div>
        </div>
        <!-- 海报页 -->
        <div class="page fc" v-if="page===8">
            <img src="img/logo.png" class="logo animate__animated animate__fadeInLeft">
            <div class="poster_area">
                <img src="img/bj4.jpg?v=1" class="poster">
                <img :src="startData.avatar" class="avatar">
                <p class="poster_p1">{{startData.nickname}}</p>
                <p class="poster_p2" v-if="p2===1">不负“粽”望<br>万般皆安好</p>
                <p class="poster_p2" v-if="p2===2">粽香四溢五月五<br>情满端午同安福</p>
                <p class="poster_p2" v-if="p2===3">一叶香草，一岁端阳<br>盼生活圆满，甜润绵长</p>
                <p class="poster_p2" v-if="p2===4">年年岁岁皆如愿<br>岁岁年年长安康</p>
                <p class="poster_p2" v-if="p2===5">肆意放“粽”，奔赴山海<br>万事胜意，岁岁长安</p>
            </div>
            <div class="button_area">
                <img src="img/button11.png" class="button11" @touchstart="getPrize">
                <img src="img/button12.png" class="button11" @touchstart="createdPoster">
            </div>
        </div>
        <!-- 礼盒详情弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="showpop===1">
                <div class="popup showpop1">
                    <img src="img/button10.png" class="back" @click="showpop=0">
                </div>
            </div>
        </transition>
        <!-- 礼盒详情弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="showpop===2">
                <div class="popup showpop2">
                    <img src="img/button10.png" class="back" @click="showpop=0">
                </div>
            </div>
        </transition>
        <!-- 提交成功弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===1">
                <div class="popup popup1">
                    <img src="img/button4_2.png" class="back" @click="reload">
                </div>
            </div>
        </transition>
        <!-- 无机会弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===2">
                <div class="popup popup2" @click="reload"></div>
            </div>
        </transition>
        <!-- 游戏成功弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===3">
                <div class="popup popup3">
                    <img src="img/button13.png" class="back" @click="goPoster">
                </div>
            </div>
        </transition>
        <!-- 游戏失败弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===4">
                <div class="popup popup4">
                    <img src="img/button4_1.png" class="back" @click="reload">
                </div>
            </div>
        </transition>
        <!-- 中奖弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===5">
                <div class="popup popup5">
                    <div class="p3" v-html="prizeData.ad"></div>
                    <div class="p4">{{prizeData.prize}}</div>
                    <img src="img/button9.png" class="back" @click="goForm">
                </div>
            </div>
        </transition>
        <!-- 未中奖弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===6">
                <div class="popup popup6">
                    <img src="img/button4_1.png" class="back" @click="reload">
                </div>
            </div>
        </transition>
        <!-- 海报弹窗 -->
        <div class="mask fc" v-if="show===7">
            <img :src="poster" class="poster">
            <div class="white"></div>
            <div class="poster_tip">*长按保存海报*</div>
            <div class="close" @click="show=0"></div>
        </div>
    </div>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/vue.js"></script>
    <!-- <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script> -->
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/jquery-3.6.0.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/howler.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/preloadjs.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/dayjs.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/html2canvas.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook1.js?v=20"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook2.js"></script>
    <script>
        const { createApp, ref, watch, nextTick } = Vue
    </script>
    <script src="game.js?v={php} echo mt_rand(1000,999999);{/php}"></script>
    <script>
        let rotation = {$rotation|raw};
        window.startData = {
            rotation:rotation,
            jihui: '{$jihui}',
            prize: '{$prize}',
            ad2: '{$ad}',
            ad: '{$ad}'.replaceAll("\n", "<br>"),
            prizeTime: '{$prizeTime}',
            userInfo: {
                name: '{$name}',
                phone: '{$phone}',
                area: '{$area}',
                address: '{$address}',
            },
            nickname: '{$nickname}',
            avatar: '{$headimgurl}',
            wlq:'{$wlq}',
            endtime: '{$endtime}',
            writeFlag: '{$writeFlag}',
            rule: `{$zt_rule|raw}`,
        }

        const app = createApp({
            setup() {
                const { on, bgClick } = useBgMusic('123.mp3')//调用景音乐
                setMockPage && setMockPage()//添加案例提示语
                const page = ref(1) //控制页面
                const show = ref(0) //控制弹窗
                const showpop = ref(0) //控制弹窗
                const { userInfo, endtime, writeFlag } = startData
                const opportunity = ref((+startData.jihui)) //控制机会
                const handleRotation = startData.rotation.map(item => item.split(',')) // 设置顶部获奖数据
                const start = () => {
                    if (endtime === '1') return vantAlert('活动未开始')
                    if (endtime === '2') return vantAlert('活动已结束')
                    if (opportunity.value >= 1) {
                        page.value = 5
                        opportunity.value--
                        defaultHttp('gamestart', { status: 1 })
                    } else {
                        show.value = 2
                    }
                }
                let gamepick = false
                const next = (pick)=>{
                    if(pick===1){
                        gamepick = true
                    }
                    page.value = 7
                }

                const edit = () => {
                    if (writeFlag === 0) {
                        return vantAlert('活动已结束');
                    } else {
                        goForm()
                    }
                }
                const goForm = () => {
                    page.value = 4
                    show.value = 0
                }
                const goPoster = () => {
                    page.value = 8
                    show.value = 0
                }

                // 登记信息功能
                const { form, popupShow, options, focus, onConfirm, check, selectedValues } = createdForm()
                form.value = userInfo
                const submit = throttle(async () => {
                    if (!check()) return
                    const res = await defaultHttp('action', Object.assign({ act: 'sub' }, form.value), { status: 1 })
                    if (res.status == 1) {
                        show.value = 1
                    } else {
                        vantAlert(res.msg)
                    }
                })
                // 刷新页面功能
                const { reload, savePage } = useReload()
                if (savePage) { page.value = +savePage }
                // 判断是否是初次进入网页,执行预加载
                const { loadStart, progress, progressShow, startFlag } = cheackStartPopup([])
                loadStart()
                if(startFlag){
                    page.value = 2
                }
                // 检查未领取
                if (startData.wlq === '1') {
                    vant.showConfirmDialog({
                        title: '温馨提示',
                        message: '您已中奖，请尽快完善领奖信息！',
                        confirmButtonText: '去领取',
                        cancelButtonText: '不领取'
                    }).then(() => {
                        show.value = 0
                        page.value = 4
                    })
                }

                const gameEnd = throttle(async (e) => {
                    const res = await defaultHttp('gameEnd', { cg:e?1:0, timer: 40-gameState.value.timer, score:gameState.value.score }, { status: 1 })
                    if (res.status === 1) {
                        show.value = 3
                    } else if(res.status === 2) {
                        show.value = 4
                    } else {
                        vantAlert(res.msg, reload)
                    }
                })

                // 抽奖逻辑
                const prizeData = ref({ prize: startData.prize, ad: startData.ad, prizeType: 0 })
                const getPrize = throttle(async () => {
                    const res = await defaultHttp('getprize', {}, { status: 1, msg: '抽中奖品', data: { prize: '水笔一支', ad: '甜润迎新，笔笔生花', prizeType: 1 } })
                    if (res.status === 1) {
                        show.value = 5
                        res.data.ad = res.data.ad.replaceAll("\\n", "<br>")
                        prizeData.value = res.data
                    } else if (res.status === 2) {
                        show.value = 6 //未中奖
                    } else {
                        vantAlert(res.msg, reload)
                    }
                })
                // 游戏逻辑
                const gameCanvas = ref(null)
                const {
                    gameState,
                    startGame,
                    initGame,
                    addEvents,
                    removeEvents,
                } = useGame(gameCanvas, gameEnd)
                // 监听页面变化
                watch(() => page.value, async (newVal) => {
                    if (newVal === 7) {
                        setTimeout(async() => {
                            await initGame(gamepick)
                            startGame(gamepick)
                        }, 0);
                    }
                }, {
                    immediate: true
                })
                const poster = ref('')
                const createdPoster = ()=>{
                    var targetDom = document.querySelector('.poster_area');
                    const setup = {
                        useCORS: true, // 使用跨域
                        height: window.innerHeight - 1, //canvas高, 高度减 1 是为了解决底部出现白线问题
                        width: targetDom.scrollWidth -1, //canvas宽
                        dpi: window.devicePixelRatio * 2 //设备像素比
                    };

                    html2canvas(document.querySelector('.poster_area'),setup).then(canvas=>{
                        poster.value = canvas.toDataURL("image/jpg")
                        show.value = 7
                    });
                }
                const p2 = ref(1)
                p2.value = randomNum(1,5)
                return {
                    startData, page, show,showpop,
                    handleRotation, edit, goForm,
                    on, bgClick,
                    start, submit, reload,
                    form, popupShow, options, focus, onConfirm, check, selectedValues,
                    prizeData, getPrize,
                    gameCanvas,
                    gameState,
                    next,
                    createdPoster,
                    poster,
                    goPoster,
                    p2
                }
            },
        });
        app.use(vant);
        app.mount('#app');
    </script>
            <!--分享-->
{include file="share"/}
</body>

</html>



