@font-face {
  font-family: '思源宋体';
  src: url(https://ztimg.hefei.cc/static/common/fonts/思源宋体.otf);
}
@font-face {
  font-family: 'HANCHANSHUTI';
  src: url(../HANCHANSHUTI·LONGCANG.OTF);
}
body,
div,
h1,
h2,
h3,
h4,
h5,
h6,
html,
li,
p,
span {
  font-size: 3.5vw;
}
.musicbtn {
  width: 9.6vw;
  height: 9.6vw;
  top: 3.6vw;
  right: 2.8vw;
  background-image: url(../img/music.png);
  z-index: 11;
}
.logo {
  width: 13.6vw;
  position: absolute;
  top: 3.6vw;
  left: 2.8vw;
  z-index: 11;
}
.warp {
  margin: 0 auto;
  min-height: 170vw;
  height: 100vh;
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: '思源宋体';
}
.warp .page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  color: #215444;
  background: url(../img/bj.jpg?v=1) no-repeat center center / 100% 100%;
}
.warp .page .title {
  margin-top: -10vw;
  width: 61.7333vw;
  z-index: 2;
}
.warp .page .title2 {
  margin-top: 10vw;
  width: 71.0667vw;
}
.warp .page .title3 {
  width: 63.2vw;
  position: absolute;
  top: 13vh;
}
.warp .page .main {
  margin-top: 4vw;
  width: 86.5333vw;
}
.warp .page .start {
  margin-top: 2vw;
  width: 59.6vw;
  height: 14.5333vw;
  flex-shrink: 0;
  background: url(../img/start.png) no-repeat center center / 100% 100%;
  z-index: 2;
}
.warp .page .step {
  width: 100%;
}
.warp .page .box_area {
  margin-top: 6vw;
  width: 78.6667vw;
  height: 96vw;
  position: relative;
  overflow: hidden;
}
.warp .page .box_area .box {
  width: 100%;
  position: relative;
  pointer-events: none;
}
.warp .page .box_area .add {
  width: 12vw;
  height: 12vw;
  background: url(../img/add.png) no-repeat center center / 100% 100%;
  position: absolute;
  top: 42%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: auto;
  cursor: pointer;
  z-index: 10;
}
.warp .page .poster {
  width: 78.6667vw;
}
.warp .page .tip {
  margin-top: 2vw;
  color: #fff;
  font-weight: bold;
  font-size: 4vw;
  letter-spacing: 0.4vw;
  transition: 0.6s;
}
.warp .page .bg2 {
  width: 100vw;
  position: absolute;
  left: 0;
  bottom: 0;
  pointer-events: none;
}
.warp .page .bg3 {
  width: 100vw;
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
}
.warp .page .button_container {
  position: absolute;
  z-index: 2;
  bottom: 7vw;
  display: flex;
  justify-content: center;
}
.warp .page .button_container .button {
  height: 10vw;
  margin: 0 6vw;
}
.warp .page .button_container .gray {
  filter: grayscale(100%);
}
.warp .page .rule {
  margin-top: 6vw;
  width: 100vw;
}
.warp .page .back {
  margin-top: 9.4667vw;
  height: 11.7333vw;
}
.warp .page .game_area {
  margin-top: 20vw;
  width: 97.2vw;
  height: 135.3333vw;
  padding: 0 5.6vw;
  background: url(../img/game_area.png) no-repeat center center / 100% 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.warp .page .game_area canvas {
  width: 100%;
  height: 100%;
}
.warp .page .game_area .data {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 0;
  width: 100%;
  position: absolute;
  top: -15vw;
}
.warp .page .game_area .data .time {
  width: 13.733vw;
  height: 13.733vw;
  border: 1px solid #fff;
  border-radius: 50%;
  color: #fff;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  line-height: 1;
  position: absolute;
  left: 6vw;
}
.warp .page .game_area .data .time span:nth-last-child(1) {
  font-weight: bold;
  font-size: 4vw;
}
.warp .page .game_area .data .score {
  width: 25.7333vw;
  height: 16.1333vw;
  background: url(../img/score.png) no-repeat center center / 100% 100%;
  color: #DBEFB4;
  font-size: 7.3333vw;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 4vw;
}
.warp .page .poster_area {
  width: 100vw;
  height: 100vh;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}
.warp .page .poster_area .poster {
  margin-top: calc((100vh - 213.3333vw) / 2);
  width: 100vw;
  height: 213.3333vw;
  flex-shrink: 0;
}
.warp .page .poster_area .poster_p1 {
  position: absolute;
  top: 27vw;
  left: 20vw;
  text-align: center;
  color: #000;
  font-weight: bold;
  font-size: 4vw;
}
.warp .page .poster_area .poster_p2 {
  position: absolute;
  top: 80vh;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 6.6667vw;
  font-weight: bold;
  white-space: nowrap;
  text-align: center;
}
.warp .page .poster_area .avatar {
  width: 14vw;
  height: 14vw;
  background-color: #fff;
  border-radius: 50%;
  position: absolute;
  top: 24vw;
  left: 4vw;
}
.warp .page .button_area {
  display: flex;
  align-items: center;
  position: absolute;
  bottom: 9.067vw;
}
.warp .page .button_area .button11 {
  height: 10vw;
  margin: 0 4vw;
}
.blur {
  filter: blur(1vw);
}
.fc {
  justify-content: center;
}
.area {
  width: 98.5333vw;
  height: 108.4vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  background: url(../img/area.png) no-repeat center center / 100% 100%;
}
.area .stit {
  position: absolute;
  top: -2vw;
  width: 40vw;
}
.area .spirit1 {
  position: absolute;
  width: 30.4vw;
  left: -9vw;
  bottom: -6vw;
}
.area .back {
  position: absolute;
  bottom: -6vw;
  width: 40vw;
}
.area .submit {
  position: absolute;
  bottom: -15vw;
  width: 40vw;
}
.area .rule {
  width: 100%;
  padding: 0 7vw;
  margin: 10vw 0 10vw;
  flex: 1;
  overflow-y: auto;
  line-height: 1.5;
  text-align: justify;
  letter-spacing: -0.1vw;
  position: relative;
}
.area .prize {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 4vw;
}
.area .prize .mt5 {
  margin-top: 2vw;
}
.area .prize .info {
  padding: 10vw 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 16vw;
  width: 70vw;
}
.area .prize .info:first-child {
  border-bottom: 1px dashed #215444;
}
.area .prize .info .p2 {
  font-size: 5vw;
  line-height: 7vw;
  max-width: 75vw;
  text-align: center;
}
.area .prize .info .jptit {
  width: 29.467vw;
  margin-bottom: 2vw;
}
.area .prize .edit {
  margin-top: 4vw;
  width: 40vw;
}
.area .form {
  width: 100%;
  padding: 16vw 5vw 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.area .form .form-item {
  margin-left: 0;
  margin-bottom: 8vw;
  display: flex;
  align-items: center;
}
.area .form .form-item label {
  width: 26vw;
  font-weight: 500;
  font-size: 5.6vw;
  white-space: nowrap;
  color: #215444;
  flex-shrink: 0;
}
.area .form .form-item div input {
  margin-bottom: 3vw;
}
.area .form .form-item div input:nth-last-child(1) {
  margin-bottom: 0;
}
.area .form .form-item .right {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.area .form .form-item input {
  margin-left: 0.8vw;
  padding-left: 2.5333vw;
  width: 50vw;
  height: 7.7333vw;
  border: 1px #215444 solid;
  flex-shrink: 0;
  opacity: 1;
  color: #215444;
  font-size: 5.6vw;
}
.area .form .form-item input::-webkit-input-placeholder {
  color: #215444;
  opacity: 0.6;
}
.area .form .form-item input:-moz-placeholder {
  color: #215444;
  opacity: 0.6;
}
.area .form .form-item input::-moz-placeholder {
  color: #215444;
  opacity: 0.6;
}
.area .form .form-item input:-ms-input-placeholder {
  color: #215444;
  opacity: 0.6;
}
.area .form .form-item #getArea {
  opacity: 0;
  position: absolute;
  z-index: -1;
}
.area .form .form-footer {
  margin-top: -10vw;
  display: flex;
  width: 200%;
  transform: scale(0.5);
  color: #215444;
}
.area .form .form-footer .fz1 {
  font-size: 8vw;
}
.area .form .form-footer p {
  font-size: 6vw;
  line-height: 1.5;
  text-align: justify;
  letter-spacing: 0.3vw;
}
.area .form .button {
  margin-top: -5vw;
  width: 30.4vw;
}
.area .form .fs {
  align-items: flex-start;
}
.area .form .fs label {
  margin-top: 0.5vw;
}
.area2 {
  background: url(../img/area2.png) no-repeat center center / 100% 100%;
  width: 88.6667vw;
  height: 140.2667vw;
}
.area2 .stit {
  top: -5vw;
}
.button5 {
  margin-top: -7vw;
  width: 40vw;
}
.mask {
  z-index: 10;
  position: fixed;
  top: 0;
  left: 0;
  min-height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(18, 45, 29, 0.3);
  transform: translateX(-50%);
  left: 50%;
  color: #2b7952;
  font-weight: 300;
}
.mask .popup {
  margin-top: -1vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.mask .popup .p2 {
  font-size: 5vw;
  margin-top: -20vw;
}
.mask .popup .back {
  width: 49.7333vw;
  position: absolute;
  bottom: -6vw;
}
.mask .popup1 {
  width: 83.4667vw;
  height: 48.4vw;
  background: url(../img/popup1.png) no-repeat center top / 100% auto;
}
.mask .popup2 {
  width: 90.1333vw;
  height: 39.6vw;
  background: url(../img/popup2.png) no-repeat center top / 100% auto;
}
.mask .popup3 {
  width: 87.8667vw;
  height: 100vw;
  background: url(../img/popup3.png) no-repeat center top / 100% 100%;
}
.mask .popup4 {
  width: 89.0667vw;
  height: 100vw;
  background: url(../img/popup4.png) no-repeat center top / 100% 100%;
}
.mask .popup5 {
  width: 87.8667vw;
  height: 100vw;
  background: url(../img/popup5.png) no-repeat center top / 100% 100%;
  padding-top: 12vw;
}
.mask .popup5 .p3 {
  margin-top: -4vw;
  font-size: 14vw;
  white-space: nowrap;
}
.mask .popup5 .p4 {
  margin-top: 6vw;
  font-size: 9.3333vw;
  white-space: nowrap;
}
.mask .popup6 {
  width: 87.8667vw;
  height: 100vw;
  background: url(../img/popup6.png) no-repeat center top / 100% 100%;
}
.mask .poster {
  width: 100vw;
  animation: sc 0.5s ease-in-out forwards;
  animation-delay: 0.5s;
}
.mask .white {
  width: 100vw;
  height: 100vh;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 3;
  background-color: white;
  /* 设置为你想要的背景颜色 */
  animation: flash 0.5s ease-in-out forwards;
  /* 定义动画名称、时长和缓动函数 */
  pointer-events: none;
}
.mask .poster_tip {
  color: #fff;
  position: absolute;
  bottom: 27vw;
  animation: flash2 1s ease-in-out forwards;
}
.mask .close {
  width: 8vw;
  height: 8vw;
  background: url(../img/close.png) no-repeat center center / 100% 100%;
  position: absolute;
  bottom: 10vw;
  animation: flash2 1s ease-in-out forwards;
}
.mask .close2 {
  width: 10vw;
  height: 10vw;
  background: url(../img/close.png) no-repeat center center / 100% 100%;
  position: absolute;
  bottom: -15vw;
}
.blink-2 {
  animation: blink-2 1s linear infinite both;
}
@keyframes blink-2 {
  0%,
  10%,
  90%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.2;
  }
}
@keyframes flash {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes flash2 {
  0%,
  99% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes sc {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(0.7) translateY(-10vw);
  }
}
