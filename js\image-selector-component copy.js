// image-selector-component.js
const ImageSelectorComponent = {
    props: {
        modelValue: {
            type: String,
            default: ''
        },
        showAddButton: {
            type: Boolean,
            default: true
        },
        minScale: {
            type: Number,
            default: 0.5
        },
        maxScale: {
            type: Number,
            default: 3
        }
    },
    emits: ['update:modelValue', 'image-change'],
    setup(props, { emit }) {
        const { ref, computed } = Vue;
        
        const fileInput = ref(null);
        const select_img = ref(props.modelValue);
        
        // 图片变换相关状态
        const imageTransform = ref({
            scale: 1,
            translateX: 0,
            translateY: 0
        });
        
        // 触摸相关状态
        const touchState = ref({
            isTouch: false,
            startDistance: 0,
            startScale: 1,
            startX: 0,
            startY: 0,
            startTranslateX: 0,
            startTranslateY: 0,
            touches: []
        });
        
        // 鼠标事件处理（桌面端支持）
        const mouseState = ref({
            isDragging: false,
            startX: 0,
            startY: 0,
            startTranslateX: 0,
            startTranslateY: 0
        });
        
        // 计算图片样式
        const imageStyle = computed(() => {
            return {
                transform: `translate(${imageTransform.value.translateX}px, ${imageTransform.value.translateY}px) scale(${imageTransform.value.scale})`,
                transformOrigin: 'center center',
                transition: touchState.value.isTouch ? 'none' : 'transform 0.3s ease'
            };
        });
        
        // 选择图片
        const selectImage = () => {
            fileInput.value.click();
        };
        
        // 处理文件选择
        const handleFileSelect = (event) => {
            const file = event.target.files[0];
            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    select_img.value = e.target.result;
                    // 重置图片变换状态
                    imageTransform.value = {
                        scale: 1,
                        translateX: 0,
                        translateY: 0
                    };
                    // 触发事件
                    emit('update:modelValue', select_img.value);
                    emit('image-change', select_img.value);
                };
                reader.readAsDataURL(file);
            }
        };
        
        // 计算两点间距离
        const getDistance = (touch1, touch2) => {
            const dx = touch1.clientX - touch2.clientX;
            const dy = touch1.clientY - touch2.clientY;
            return Math.sqrt(dx * dx + dy * dy);
        };
        
        // 触摸开始
        const handleTouchStart = (event) => {
            event.preventDefault();
            touchState.value.isTouch = true;
            touchState.value.touches = Array.from(event.touches);
            
            if (event.touches.length === 1) {
                // 单指拖动
                touchState.value.startX = event.touches[0].clientX;
                touchState.value.startY = event.touches[0].clientY;
                touchState.value.startTranslateX = imageTransform.value.translateX;
                touchState.value.startTranslateY = imageTransform.value.translateY;
            } else if (event.touches.length === 2) {
                // 双指缩放
                touchState.value.startDistance = getDistance(event.touches[0], event.touches[1]);
                touchState.value.startScale = imageTransform.value.scale;
            }
        };
        
        // 触摸移动
        const handleTouchMove = (event) => {
            event.preventDefault();
            
            if (event.touches.length === 1 && touchState.value.touches.length === 1) {
                // 单指拖动
                const deltaX = event.touches[0].clientX - touchState.value.startX;
                const deltaY = event.touches[0].clientY - touchState.value.startY;
                
                imageTransform.value.translateX = touchState.value.startTranslateX + deltaX;
                imageTransform.value.translateY = touchState.value.startTranslateY + deltaY;
            } else if (event.touches.length === 2 && touchState.value.touches.length === 2) {
                // 双指缩放
                const currentDistance = getDistance(event.touches[0], event.touches[1]);
                const scale = (currentDistance / touchState.value.startDistance) * touchState.value.startScale;
                
                // 限制缩放范围
                imageTransform.value.scale = Math.max(props.minScale, Math.min(props.maxScale, scale));
            }
        };
        
        // 触摸结束
        const handleTouchEnd = (event) => {
            event.preventDefault();
            touchState.value.isTouch = false;
            touchState.value.touches = Array.from(event.touches);
        };
        
        // 鼠标按下
        const handleMouseDown = (event) => {
            event.preventDefault();
            mouseState.value.isDragging = true;
            mouseState.value.startX = event.clientX;
            mouseState.value.startY = event.clientY;
            mouseState.value.startTranslateX = imageTransform.value.translateX;
            mouseState.value.startTranslateY = imageTransform.value.translateY;
        };
        
        // 鼠标移动
        const handleMouseMove = (event) => {
            if (!mouseState.value.isDragging) return;
            event.preventDefault();
            
            const deltaX = event.clientX - mouseState.value.startX;
            const deltaY = event.clientY - mouseState.value.startY;
            
            imageTransform.value.translateX = mouseState.value.startTranslateX + deltaX;
            imageTransform.value.translateY = mouseState.value.startTranslateY + deltaY;
        };
        
        // 鼠标释放
        const handleMouseUp = (event) => {
            event.preventDefault();
            mouseState.value.isDragging = false;
        };
        
        // 滚轮缩放
        const handleWheel = (event) => {
            event.preventDefault();
            const delta = event.deltaY > 0 ? -0.1 : 0.1;
            const newScale = imageTransform.value.scale + delta;
            imageTransform.value.scale = Math.max(props.minScale, Math.min(props.maxScale, newScale));
        };
        
        // 监听props变化
        Vue.watch(() => props.modelValue, (newValue) => {
            select_img.value = newValue;
        });

        // 暴露方法给父组件
        const { expose } = Vue.getCurrentInstance() || {};
        if (expose) {
            expose({
                selectImage
            });
        }

        return {
            fileInput,
            select_img,
            imageStyle,
            selectImage,
            handleFileSelect,
            handleTouchStart,
            handleTouchMove,
            handleTouchEnd,
            handleMouseDown,
            handleMouseMove,
            handleMouseUp,
            handleWheel
        };
    },
    template: `
        <div class="image-selector-container">
            <img v-if="select_img" :src="select_img" class="select_img"
                 :style="imageStyle"
                 @touchstart="handleTouchStart"
                 @touchmove="handleTouchMove"
                 @touchend="handleTouchEnd"
                 @mousedown="handleMouseDown"
                 @mousemove="handleMouseMove"
                 @mouseup="handleMouseUp"
                 @wheel="handleWheel">
            <div v-if="!select_img && showAddButton" class="add" @click="selectImage"></div>
            <input type="file" ref="fileInput" @change="handleFileSelect" accept="image/*" style="display: none;">
        </div>
    `
};

// 将组件挂载到全局
window.ImageSelectorComponent = ImageSelectorComponent;
