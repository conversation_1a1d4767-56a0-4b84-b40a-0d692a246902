<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>组件方法调用示例</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-area {
            width: 300px;
            height: 200px;
            border: 2px dashed #ccc;
            position: relative;
            margin: 20px auto;
            overflow: hidden;
            background: #fafafa;
        }
        .image-selector-container {
            width: 100%;
            height: 100%;
            position: relative;
        }
        .select_img {
            max-width: 100%;
            max-height: 100%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            pointer-events: auto;
            touch-action: none;
            user-select: none;
            cursor: move;
        }
        .add {
            width: 50px;
            height: 50px;
            background: #007bff;
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            font-weight: bold;
        }
        .add:hover {
            background: #0056b3;
        }
        .btn-group {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.secondary {
            background: #6c757d;
        }
        .btn.secondary:hover {
            background: #545b62;
        }
        .info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div id="app" class="container">
        <h1>组件方法调用示例</h1>
        
        <div class="demo-area">
            <image-selector 
                ref="imageSelectorRef" 
                v-model="selectedImage" 
                @image-change="onImageChange"
                :show-add-button="showAddButton">
            </image-selector>
        </div>
        
        <div class="btn-group">
            <button class="btn" @click="callSelectImage">📁 通过ref调用选择</button>
            <button class="btn secondary" @click="toggleAddButton">{{ showAddButton ? '隐藏' : '显示' }}+号按钮</button>
            <button class="btn secondary" @click="clearImage">🗑️ 清除图片</button>
        </div>
        
        <div class="info">
            <h3>功能演示：</h3>
            <p><strong>通过ref调用选择：</strong>点击按钮调用组件的selectImage()方法</p>
            <p><strong>隐藏/显示+号按钮：</strong>控制组件内部的添加按钮显示</p>
            <p><strong>清除图片：</strong>清空v-model绑定的数据</p>
            
            <h3>代码示例：</h3>
            <div class="code">
&lt;image-selector 
    ref="imageSelectorRef" 
    v-model="selectedImage" 
    @image-change="onImageChange"
    :show-add-button="showAddButton"&gt;
&lt;/image-selector&gt;
            </div>
            
            <div class="code">
// 通过ref调用组件方法
const callSelectImage = () => {
    if (imageSelectorRef.value) {
        imageSelectorRef.value.selectImage();
    }
};
            </div>
        </div>
        
        <div v-if="selectedImage" class="info">
            <h3>当前状态：</h3>
            <p>✅ 已选择图片</p>
            <p>数据长度: {{ selectedImage.length }} 字符</p>
        </div>
        
        <div v-else class="info">
            <h3>当前状态：</h3>
            <p>❌ 未选择图片</p>
        </div>
    </div>

    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="js/image-selector-component.js"></script>
    <script>
        const { createApp, ref } = Vue;

        createApp({
            components: {
                'image-selector': ImageSelectorComponent
            },
            setup() {
                const selectedImage = ref('');
                const imageSelectorRef = ref(null);
                const showAddButton = ref(true);
                
                const onImageChange = (imageData) => {
                    console.log('图片已选择:', imageData ? '有数据' : '无数据');
                };
                
                // 通过ref调用组件的selectImage方法
                const callSelectImage = () => {
                    if (imageSelectorRef.value) {
                        imageSelectorRef.value.selectImage();
                        console.log('通过ref调用了selectImage方法');
                    } else {
                        console.error('组件ref未找到');
                    }
                };
                
                const toggleAddButton = () => {
                    showAddButton.value = !showAddButton.value;
                };
                
                const clearImage = () => {
                    selectedImage.value = '';
                };
                
                return {
                    selectedImage,
                    imageSelectorRef,
                    showAddButton,
                    onImageChange,
                    callSelectImage,
                    toggleAddButton,
                    clearImage
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
