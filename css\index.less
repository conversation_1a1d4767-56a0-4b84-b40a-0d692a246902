// 取消微信浏览器点击的蓝色背景
// *{
//     -webkit-touch-callout:none;
//     -webkit-user-select:none; 
//     -moz-user-select:none;
//     -ms-user-select:none;
//     user-select:none;
//     -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
//     -webkit-user-select: none;
//     -moz-user-focus: none;
//     -moz-user-select: none
// }

@font-face {
    font-family: '思源宋体';
    src: url(https://ztimg.hefei.cc/static/common/fonts/思源宋体.otf);
}
@font-face {
    font-family: 'HANCHANSHUTI';
    src: url(../HANCHANSHUTI·LONGCANG.OTF);
}

body,
div,
h1,
h2,
h3,
h4,
h5,
h6,
html,
li,
p,
span {
    font-size: 3.5vw
}

@formcolor: #215444;


.musicbtn {
    width: 9.6vw;
    height: 9.6vw;
    top: 3.6vw;
    right: 2.8vw;
    background-image: url(../img/music.png);
    z-index: 11;
}
.logo {
    width: 13.6vw;
    position: absolute;
    top: 3.6vw;
    left: 2.8vw;
    z-index: 11;
}

.warp {
    margin: 0 auto;
    min-height: 170vw;
    height: 100vh;
    width: 100vw;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: '思源宋体';
    .page {
        width: 100%;
        height: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 2;
        color: #215444;
        background:  url(../img/bj.jpg?v=1) no-repeat center center/100% 100%;

        .title {
            margin-top: -10vw;
            width: 61.7333vw;
            z-index: 2;
        }
        .title2{
            margin-top: 10vw;
            width: 71.0667vw;
        }
        .title3{
            width: 63.2vw;
            position: absolute;
            top: 13vh;
        }
        .main{
            margin-top: 4vw;
            width: 86.5333vw;
        }
        .start {
            margin-top: 2vw;
            width: 59.6vw;
            height: 14.5333vw;
            flex-shrink: 0;
            background: url(../img/start.png) no-repeat center center/100% 100%;
            z-index: 2;
        }
        .step{
            width: 100%;
        }
        .box_area{
            margin-top: 6vw;
            width: 78.6667vw;
            height: 96vw;
            position: relative;
            overflow: hidden;
            .box{
                width: 100%;
                position: relative;
                pointer-events: none;
            }
            .add{
                width: 12vw;
                height: 12vw;
                background: url(../img/add.png) no-repeat center center/100% 100%;
                position: absolute;
                top: 42%;
                left: 50%;
                transform: translate(-50%,-50%);
                pointer-events: auto;
                cursor: pointer;
                z-index: 10;
            }
        }
        .poster{
            width: 78.6667vw;
        }
        .tip{
            margin-top: 2vw;
            color: #fff;
            font-weight: bold;
            font-size: 4vw;
            letter-spacing: 0.4vw;
            transition: 0.6s;
        }
        .bg2{
            width: 100vw;
            position: absolute;
            left: 0;
            bottom: 0;
            pointer-events: none;
        }
        .bg3{
            width: 100vw;
            position: absolute;
            left: 0;
            top: 0;
            pointer-events: none;
        }
        .button_container{
            position: absolute;
            z-index: 2;
            bottom: 7vw;
            display: flex;
            justify-content: center;
            .button{
                // width: 34.8vw;
                height: 10vw;
                margin: 0 6vw;
            }
            .gray{
                filter: grayscale(100%);
            }
        }
        .rule{
            margin-top: 6vw;
            width: 100vw;
        }
        .back{
            margin-top: 9.4667vw;
            height: 11.7333vw;
        }
        .game_area {
            margin-top: 20vw;
            width: 97.2vw;
            height: 135.3333vw;
            padding: 0 5.6vw;
            background: url(../img/game_area.png) no-repeat center center/100% 100%;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            canvas {
                width: 100%;
                height: 100%;
            }
            .data{
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 0 0;
                width: 100%;
                position: absolute;
                top: -15vw;
                .time{
                    width: 13.733vw;
                    height: 13.733vw;
                    border: 1px solid #fff;
                    border-radius: 50%;
                    color: #fff;
                    text-align: center;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    line-height: 1;
                    position: absolute;
                    left: 6vw;
                    span{
                        &:nth-last-child(1){
                            font-weight: bold;
                            font-size: 4vw;
                        }
                    }
                }
                .score{
                    width: 25.7333vw;
                    height: 16.1333vw;
                    background: url(../img/score.png) no-repeat center center/100% 100%;
                    color: #DBEFB4;
                    font-size: 7.3333vw;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    padding-top: 4vw;
                }
            }
        }
        .poster_area {
            width: 100vw;
            height: 100vh;
            flex-shrink: 0;
            position: relative;
            overflow: hidden;

            .poster {
                margin-top: calc( (100vh - 213.3333vw) / 2 );
                width: 100vw;
                height: 213.3333vw;
                flex-shrink: 0;
            }

            .poster_p1 {
                position: absolute;
                top: 27vw;
                left: 20vw;
                text-align: center;
                color: #000;
                font-weight: bold;
                font-size: 4vw;
            }
            .poster_p2{
                position: absolute;
                top: 80vh;
                left: 50%;
                transform: translate(-50%,-50%);
                font-size: 6.6667vw;
                font-weight: bold;
                white-space: nowrap;
                text-align: center;
            }
            .avatar{
                width: 14vw;
                height: 14vw;
                background-color: #fff;
                border-radius: 50%;
                position: absolute;
                top: 24vw;
                left: 4vw;
            }

        }
        .button_area {
            display: flex;
            align-items: center;
            position: absolute;
            bottom: 9.067vw;

            .button11 {
                height: 10vw;
                margin: 0 4vw;
            }
        }
    }
}
.blur{
    filter: blur(1vw);
}
.fc {
    justify-content: center;
}

.area {
    width: 98.5333vw;
    height: 108.4vw;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    background: url(../img/area.png) no-repeat center center/100% 100%;

    .stit {
        position: absolute;
        top: -2vw;
        width: 40vw;
    }
    .spirit1{
        position: absolute;
        width: 30.4vw;
        left: -9vw;
        bottom: -6vw;
    }

    .back {
        position: absolute;
        bottom: -6vw;
        width: 40vw;
    }

    .submit {
        position: absolute;
        bottom: -15vw;
        width: 40vw;
    }

    .rule {
        width: 100%;
        padding: 0 7vw;
        margin: 10vw 0 10vw;
        flex: 1;
        overflow-y: auto;
        line-height: 1.5;
        text-align: justify;
        letter-spacing: -0.1vw;
        position: relative;
    }

    .prize {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-top: 4vw;

        .mt5 {
            margin-top: 2vw;
        }

        .info {
            padding: 10vw 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 16vw;
            width: 70vw;
            &:first-child{
                border-bottom: 1px dashed #215444;
            }

            .p2 {
                font-size: 5vw;
                line-height: 7vw;
                max-width: 75vw;
                text-align: center;
            }

            .jptit {
                width: 29.467vw;
                margin-bottom: 2vw;
            }
        }

        .edit {
            margin-top: 4vw;
            width: 40vw;
        }
    }


    .form {
        width: 100%;
        padding: 16vw 5vw 0;
        display: flex;
        flex-direction: column;
        align-items: center;

        .form-item {
            margin-left: 0;
            margin-bottom: 8vw;
            display: flex;
            align-items: center;

            label {
                width: 26vw;
                font-weight: 500;
                font-size: 5.6vw;
                white-space: nowrap;
                color: @formcolor;
                flex-shrink: 0;
            }

            div {

                input {
                    margin-bottom: 3vw;
                    
                }

                input:nth-last-child(1) {
                    margin-bottom: 0;
                }
            }

            .right {
                flex-shrink: 0;
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            input {
                margin-left: 0.8vw;
                padding-left: 2.5333vw;
                width: 50vw;
                height: 7.7333vw;
                border: 1px @formcolor solid;
                flex-shrink: 0;
                // border-radius: 0.8vw;
                opacity: 1;
                color: @formcolor;
                font-size: 5.6vw;

                &::-webkit-input-placeholder {
                    color: @formcolor;
                    opacity: 0.6;
                }

                &:-moz-placeholder {
                    color: @formcolor;
                    opacity: 0.6;
                }

                &::-moz-placeholder {
                    color: @formcolor;
                    opacity: 0.6;
                }

                &:-ms-input-placeholder {
                    color: @formcolor;
                    opacity: 0.6;
                }
            }

            #getArea {
                opacity: 0;
                position: absolute;
                z-index: -1;
            }

        }

        .form-footer {
            margin-top: -10vw;
            display: flex;
            width: 200%;
            transform: scale(0.5);
            color: @formcolor;

            .fz1 {
                font-size: 8vw;
            }

            p {
                font-size: 6vw;
                line-height: 1.5;
                text-align: justify;
                letter-spacing: 0.3vw;
            }
        }

        .button {
            margin-top: -5vw;
            width: 30.4vw;
        }

        .fs {
            align-items: flex-start;

            label {
                margin-top: 0.5vw;
            }
        }
    }
}

.area2 {
    background: url(../img/area2.png) no-repeat center center/100% 100%;
    width: 88.6667vw;
    height: 140.2667vw;
    .stit{
        top: -5vw;
    }
}
.button5{
    margin-top: -7vw;
    width: 40vw;
}


.mask {
    z-index: 10;
    position: fixed;
    top: 0;
    left: 0;
    min-height: 100vh;
    width: 100vw;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: rgba(18, 45, 29, 0.3);
    // backdrop-filter: blur(4px);
    transform: translateX(-50%);
    left: 50%;
    color: #2b7952;
    font-weight: 300;
    .popup {
        margin-top: -1vw;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
        .p2{
            font-size: 5vw;
            margin-top: -20vw;
        }
        .back {
            width: 49.7333vw;
            position: absolute;
            bottom: -6vw;
        }

    }

    .popup1 {
        width: 83.4667vw;
        height: 48.4vw;
        background: url(../img/popup1.png) no-repeat center top / 100% auto;
    }

    .popup2 {
        width: 90.1333vw;
        height: 39.6vw;
        background: url(../img/popup2.png) no-repeat center top / 100% auto;
    }
    .popup3 {
        width: 87.8667vw;
        height: 100vw;
        background: url(../img/popup3.png) no-repeat center top / 100% 100%;
    }

    .popup4 {
        width: 89.0667vw;
        height: 100vw;
        background: url(../img/popup4.png) no-repeat center top / 100% 100%;
    }
    .popup5 {
        width: 87.8667vw;
        height: 100vw;
        background: url(../img/popup5.png) no-repeat center top / 100% 100%;
        padding-top: 12vw;
        .p3{
            margin-top: -4vw;
            font-size: 14vw;
            white-space: nowrap;
        }
        .p4{
            margin-top: 6vw;
            font-size: 9.3333vw;
            white-space: nowrap;
        }
    }
    .popup6 {
        width: 87.8667vw;
        height: 100vw;
        background: url(../img/popup6.png) no-repeat center top / 100% 100%;
    }
    .poster {
        width: 100vw;
        animation: sc 0.5s ease-in-out forwards;
        animation-delay: 0.5s;
    }

    .white {
        width: 100vw;
        height: 100vh;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 3;
        background-color: white;
        /* 设置为你想要的背景颜色 */
        animation: flash 0.5s ease-in-out forwards;
        /* 定义动画名称、时长和缓动函数 */
        pointer-events: none;
    }

    .poster_tip {
        color: #fff;
        position: absolute;
        bottom: 27vw;
        animation: flash2 1s ease-in-out forwards;
    }

    .close {
        width: 8vw;
        height: 8vw;
        background: url(../img/close.png) no-repeat center center/100% 100%;
        position: absolute;
        bottom: 10vw;
        animation: flash2 1s ease-in-out forwards;
    }
    .close2 {
        width: 10vw;
        height: 10vw;
        background: url(../img/close.png) no-repeat center center/100% 100%;
        position: absolute;
        bottom: -15vw;
    }
}


.blink-2 {
    animation: blink-2 1s linear infinite both;
}

@keyframes blink-2 {

    0%,
    10%,
    90%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.2;
    }
}

@keyframes flash {
    0% {
        opacity: 1;
    }

    100% {
        opacity: 0;
    }
}

@keyframes flash2 {

    0%,
    99% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

@keyframes sc {
    0% {
        transform: scale(1);
    }

    100% {
        transform: scale(0.7) translateY(-10vw);
    }
}